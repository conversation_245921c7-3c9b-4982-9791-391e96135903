<?php
/**
 * 测试真实的JayaPay提现回调数据
 */

echo "=== 测试真实JayaPay提现回调数据 ===\n\n";

// 从日志中提取的真实JayaPay提现回调数据
$realWithdrawalCallbackData = [
    "bankCode" => "014",
    "fee" => "7000",
    "description" => "代付下单",
    "orderNum" => "202508031506301400899836",
    "feeType" => "1",
    "number" => "*************",
    "platOrderNum" => "YBL1951902536864641079",
    "money" => "50000",
    "statusMsg" => "FAILED",
    "name" => "aa",
    "platSign" => "JI7wpyqiMryARy6GYHungd9XsMqg4GVvXSP7gG2aCxG6L5MK/fBlmm5DPaSkamFRqEBYDIWfkf+1rpKUwuCxyA2w6tg8KLHmjbIgz28c9u6G4eIx3cY3aJ3D0M2p4xxIKSz9mnycKKLO/kd+/fJycZdhAaxL4vRmEleXn1ApTH8=",
    "status" => "4"
];

echo "真实提现回调数据:\n";
echo "订单号: {$realWithdrawalCallbackData['orderNum']}\n";
echo "平台订单号: {$realWithdrawalCallbackData['platOrderNum']}\n";
echo "金额: {$realWithdrawalCallbackData['money']} IDR\n";
echo "手续费: {$realWithdrawalCallbackData['fee']} IDR\n";
echo "状态: {$realWithdrawalCallbackData['statusMsg']}\n";
echo "银行代码: {$realWithdrawalCallbackData['bankCode']}\n";
echo "账户: {$realWithdrawalCallbackData['number']}\n";
echo "用户: {$realWithdrawalCallbackData['name']}\n\n";

// 测试环境
$testEnvironments = [
    [
        'name' => '本地环境测试',
        'url' => 'http://dianzhan_nginx/api/transaction/unifiedWithdrawalCallback'
    ],
    [
        'name' => '线上环境测试',
        'url' => 'https://www.lotteup.com/api/transaction/unifiedWithdrawalCallback'
    ]
];

foreach ($testEnvironments as $env) {
    echo "=== {$env['name']} ===\n";
    echo "回调地址: {$env['url']}\n";
    
    $postData = http_build_query($realWithdrawalCallbackData);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $env['url']);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/x-www-form-urlencoded',
        'User-Agent: JayaPay-Callback/1.0'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    $info = curl_getinfo($ch);
    curl_close($ch);
    
    echo "HTTP状态码: {$httpCode}\n";
    echo "响应时间: " . round($info['total_time'], 2) . "秒\n";
    
    if ($error) {
        echo "❌ CURL错误: {$error}\n";
    } else {
        echo "响应内容: {$response}\n";
        
        if ($httpCode === 200) {
            if ($response === 'SUCCESS') {
                echo "✅ 提现回调处理成功！\n";
                echo "✅ 签名验证通过！\n";
                echo "✅ 修复在{$env['name']}正常工作！\n";
            } elseif ($response === 'fail') {
                echo "⚠️ 回调返回fail（可能是签名验证失败或订单状态问题）\n";
            } else {
                echo "⚠️ 回调接收成功，处理结果: {$response}\n";
            }
        } else {
            echo "❌ 回调请求失败，HTTP状态码: {$httpCode}\n";
        }
    }
    
    echo "\n" . str_repeat("-", 60) . "\n\n";
    
    // 等待1秒避免请求过快
    sleep(1);
}

echo "=== 真实提现回调测试完成 ===\n";
echo "\n说明：\n";
echo "- 使用了从线上日志中提取的真实JayaPay提现回调数据\n";
echo "- 测试了本地和线上环境的提现回调处理\n";
echo "- 验证了提现回调的签名验证修复效果\n";
