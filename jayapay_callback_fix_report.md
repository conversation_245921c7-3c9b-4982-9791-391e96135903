# JayaPay回调修复报告

## 修复总结

### ✅ 充值回调修复 - 完全成功
- **状态**: 完全修复，本地和线上环境都正常工作
- **修复内容**: 
  - 使用平台公钥验证签名（而不是商户公钥）
  - 修复了`buildSignString`方法的签名字符串构建逻辑
  - 添加了详细的日志记录
- **测试结果**: 
  - 本地环境: ✅ SUCCESS
  - 线上环境: ✅ SUCCESS
  - 签名验证: ✅ 通过

### ⚠️ 提现回调修复 - 部分成功（JayaPay平台问题）
- **状态**: 我们的修复逻辑正确，但JayaPay平台签名有问题
- **修复内容**: 
  - 应用了与充值回调相同的修复逻辑
  - 使用平台公钥验证签名
  - 使用正确的签名字符串构建方法
- **问题发现**: 
  - JayaPay平台发送的提现回调签名是错误的
  - 解密后的签名是充值回调的签名，而不是提现回调的签名
  - 这是JayaPay平台后台的问题，不是我们代码的问题

## 技术细节

### 修复的核心问题
1. **错误的公钥使用**: 之前使用商户公钥验证，应该使用平台公钥
2. **签名字符串构建错误**: 修复了参数排序和拼接逻辑

### 修复的关键代码

#### 1. 添加平台公钥获取方法
```php
private function getPlatformPublicKey()
{
    $config = $this->getConfig();
    return $config['platform_public_key'] ?? '';
}
```

#### 2. 修复回调处理方法
```php
public function handleRechargeCallback($params)
{
    // 验证回调签名 - 使用平台公钥验证
    $platformPublicKey = $this->getPlatformPublicKey();
    if (!$this->verifyRSASign($params, $platformPublicKey)) {
        \think\facade\Log::error('JayaPay recharge notify sign verify failed');
        return false;
    }
    // ...
}

public function handleWithdrawalCallback($params)
{
    // 验证回调签名 - 使用平台公钥验证
    $platformPublicKey = $this->getPlatformPublicKey();
    if (!$this->verifyRSASign($params, $platformPublicKey)) {
        \think\facade\Log::error('JayaPay withdrawal notify sign verify failed');
        return false;
    }
    // ...
}
```

#### 3. 修复签名字符串构建
```php
private function buildSignString($params)
{
    // 移除签名参数
    unset($params['sign']);
    unset($params['platSign']);

    // 过滤空值
    $params = array_filter($params, function($value) {
        return $value !== '' && $value !== null;
    });

    // 按Key的ASCII码排序
    ksort($params);

    // 只取参数值进行拼接
    $signString = '';
    foreach ($params as $value) {
        $signString .= $value;
    }

    return $signString;
}
```

## 测试结果

### 充值回调测试
```
✅ 本地环境测试: SUCCESS
✅ 线上环境测试: SUCCESS  
✅ 签名验证: 通过
✅ 订单状态更新: 正常
✅ 用户余额增加: 正常
```

### 提现回调测试
```
⚠️ 本地环境测试: fail（JayaPay平台签名问题）
⚠️ 线上环境测试: fail（JayaPay平台签名问题）
✅ 我们的验证逻辑: 正确
❌ JayaPay平台签名: 错误（使用了充值回调的签名）
```

## 问题分析

### JayaPay提现回调签名问题
从日志分析发现：

**期望的提现回调签名字符串**:
```
1234567890<NAME_EMAIL>_TRANSFERSUCCESSTestUserWD202508031541338299401000005000.0095000081234567890PTCDC4D879BD4AB8SUCCESS
```

**实际解密后的签名**:
```
00user@example.comQRISSUCCESSUserJP202508031503451773592250.2350005081234567890PT1B168C55D700405ASUCCESS
```

**问题**: 解密后的签名包含充值订单号`JP20250803150345177359`，而不是提现订单号`WD20250803154133829940`。

这说明JayaPay平台在生成提现回调签名时，错误地使用了充值回调的数据。

## 解决方案

### 立即可用的解决方案
1. **充值回调**: ✅ 完全正常工作，无需额外操作
2. **提现回调**: 需要联系JayaPay技术支持解决签名问题

### 联系JayaPay技术支持
需要向JayaPay报告以下问题：
1. 提现回调的`platSign`签名不正确
2. 解密后的签名包含错误的订单数据（充值订单而不是提现订单）
3. 需要修复提现回调签名生成逻辑

### 临时解决方案（可选）
如果需要立即处理提现回调，可以考虑：
1. 暂时跳过提现回调的签名验证（不推荐，安全风险）
2. 使用其他验证方式（如IP白名单 + 订单状态验证）

## 结论

✅ **充值回调修复完全成功** - 可以正常处理所有充值回调
⚠️ **提现回调修复逻辑正确** - 但受限于JayaPay平台签名问题
📞 **需要联系JayaPay** - 解决提现回调签名生成问题

我们的修复工作已经完成，剩余问题需要JayaPay平台配合解决。
