<?php
/**
 * 测试JayaPay提现回调功能
 */

echo "=== 测试JayaPay提现回调功能 ===\n\n";

// 测试场景：本地环境和线上环境
$testEnvironments = [
    [
        'name' => '本地环境测试',
        'url' => 'http://dianzhan_nginx/api/transaction/unifiedWithdrawalCallback'
    ],
    [
        'name' => '线上环境测试', 
        'url' => 'https://www.lotteup.com/api/transaction/unifiedWithdrawalCallback'
    ]
];

// JayaPay提现回调数据示例
$withdrawalCallbackData = [
    "msg" => "SUCCESS",
    "code" => "00",
    "method" => "BANK_TRANSFER", 
    "orderNum" => "WD" . date('YmdHis') . rand(100000, 999999), // 提现订单号
    "platOrderNum" => "PT" . strtoupper(substr(md5(time()), 0, 14)),
    "payFee" => "5000.00",
    "payMoney" => "95000", // 实际到账金额
    "originalAmount" => "100000", // 原始提现金额
    "phone" => "08**********",
    "name" => "TestUser",
    "bankCode" => "BCA",
    "bankAccount" => "**********",
    "bankName" => "Bank Central Asia",
    "platSign" => "ZcEJYv3yWINeoTCx6Zy12bWvYWOzi967FtelgdL7v4HUvaLI8DOFVHb34m2ZvUEt/tTCu1ZzGaUFJfI5fDSNz2mnkjR8GqTeEZdDH3qLILprQWUA2ULtfAlYpOxEdR6jYkNeaMRicYlb8XH64D3PjBBaaHdaPPh8BhIeU6rv5js=",
    "email" => "<EMAIL>",
    "status" => "SUCCESS"
];

echo "提现回调测试数据:\n";
echo "订单号: {$withdrawalCallbackData['orderNum']}\n";
echo "平台订单号: {$withdrawalCallbackData['platOrderNum']}\n";
echo "原始金额: {$withdrawalCallbackData['originalAmount']} IDR\n";
echo "实际到账: {$withdrawalCallbackData['payMoney']} IDR\n";
echo "手续费: {$withdrawalCallbackData['payFee']} IDR\n";
echo "银行: {$withdrawalCallbackData['bankName']} ({$withdrawalCallbackData['bankCode']})\n";
echo "账户: {$withdrawalCallbackData['bankAccount']}\n\n";

foreach ($testEnvironments as $env) {
    echo "=== {$env['name']} ===\n";
    echo "回调地址: {$env['url']}\n";
    
    $postData = http_build_query($withdrawalCallbackData);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $env['url']);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/x-www-form-urlencoded',
        'User-Agent: JayaPay-Callback/1.0'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    $info = curl_getinfo($ch);
    curl_close($ch);
    
    echo "HTTP状态码: {$httpCode}\n";
    echo "响应时间: " . round($info['total_time'], 2) . "秒\n";
    
    if ($error) {
        echo "❌ CURL错误: {$error}\n";
    } else {
        echo "响应内容: {$response}\n";
        
        if ($httpCode === 200) {
            if ($response === 'SUCCESS') {
                echo "✅ 提现回调处理成功！\n";
                echo "✅ 签名验证通过！\n";
            } elseif ($response === 'fail') {
                echo "⚠️ 回调返回fail（可能是订单不存在，但接口正常）\n";
            } else {
                echo "⚠️ 回调接收成功，处理结果: {$response}\n";
            }
        } else {
            echo "❌ 回调请求失败，HTTP状态码: {$httpCode}\n";
        }
    }
    
    echo "\n" . str_repeat("-", 60) . "\n\n";
    
    // 等待1秒避免请求过快
    sleep(1);
}

echo "=== 提现回调测试完成 ===\n";
echo "\n说明：\n";
echo "- 测试了本地和线上环境的提现回调接口\n";
echo "- 验证了JayaPay提现回调的签名验证功能\n";
echo "- 如果返回SUCCESS说明提现回调修复成功\n";
echo "- 如果返回fail可能是订单不存在，但说明接口正常工作\n";
