<?php
/**
 * 验签测试脚本
 * 使用日志中的真实对调数据进行验签测试
 * 使用TransactionService和JayaPayService的验签方法
 */

// 模拟ThinkPHP环境
if (!class_exists('think\facade\Log')) {
    class MockLog {
        public static function info($msg) {
            echo "[INFO] $msg\n";
        }
        public static function error($msg) {
            echo "[ERROR] $msg\n";
        }
    }
    
    // 创建别名
    if (!class_exists('think\facade\Log')) {
        class_alias('MockLog', 'think\facade\Log');
    }
}

// 模拟model函数
if (!function_exists('model')) {
    function model($name) {
        return new stdClass();
    }
}

// 引入Service类
require_once 'application/common/service/WatchPayService.php';
require_once 'application/common/service/JayaPayService.php';
require_once 'application/common/service/TransactionService.php';

class SignVerificationTest
{
    private $watchPayService;
    private $jayaPayService;
    private $transactionService;
    
    public function __construct()
    {
        $this->watchPayService = new \app\common\service\WatchPayService();
        $this->jayaPayService = new \app\common\service\JayaPayService();
        $this->transactionService = new \app\common\service\TransactionService();
    }
    
    /**
     * 测试WatchPay MD5验签
     */
    public function testWatchPayMD5Sign()
    {
        echo "=== WatchPay MD5验签测试 ===\n";
        
        // 从日志中提取的真实数据
        $testCases = [
            // 提现回调验签失败案例
            [
                'name' => '提现回调验签',
                'params' => [
                    'tradeResult' => '1',
                    'merTransferId' => '202508031110458799654977',
                    'merNo' => '200999218',
                    'tradeNo' => '708578988',
                    'transferAmount' => '50000.00',
                    'signType' => 'MD5',
                    'applyDate' => '2025-08-03 11:11:11',
                    'version' => '1.0',
                    'respCode' => 'SUCCESS',
                    'sign' => '539c3856dda6c71235d841683c83affe'
                ],
                'key' => 'DLVPXKVOMNOFVEN2AGEQWQUASPN2EPWQ'
            ],
            // 充值回调验签（正式商户数据）
            [
                'name' => '充值回调验签（正式商户）',
                'params' => [
                    'tradeResult' => '1',
                    'oriAmount' => '50000.00',
                    'amount' => '50000.00',
                    'mchId' => '200999218',  // 正式商户ID
                    'orderNo' => '20000912675228',
                    'mchOrderNo' => 'GP202508031243391859',
                    'signType' => 'MD5',
                    'orderDate' => '2025-08-03 12:43:39'
                    // 不包含sign，让系统生成正确的签名
                ],
                'key' => 'YINGPTEMYQ7BAKOSHCAYZESK1WKU8XMK'  // 正式商户密钥
            ]
        ];
        
        foreach ($testCases as $testCase) {
            echo "\n--- {$testCase['name']} ---\n";
            $params = $testCase['params'];
            $key = $testCase['key'];

            // 如果没有签名，先生成正确的签名
            if (!isset($params['sign'])) {
                $correctSign = $this->watchPayService->generateMD5Sign($params, $key);
                $params['sign'] = $correctSign;
                echo "生成的正确签名: {$correctSign}\n";
            } else {
                $receivedSign = $params['sign'];
                echo "接收到的签名: {$receivedSign}\n";
            }

            // 使用WatchPayService的验签方法
            echo "\n使用WatchPayService验签:\n";
            try {
                $verifyResult = $this->watchPayService->verifyMD5Sign($params, $key);
                echo "验签结果: " . ($verifyResult ? '✓ 成功' : '✗ 失败') . "\n";

                // 生成签名进行对比
                $expectedSign = $this->watchPayService->generateMD5Sign($params, $key);
                echo "期望签名: {$expectedSign}\n";
                echo "签名比较: " . ($expectedSign === $params['sign'] ? '✓ 匹配' : '✗ 不匹配') . "\n";

            } catch (Exception $e) {
                echo "验签异常: " . $e->getMessage() . "\n";
            }
        }
    }
    
    /**
     * 测试JayaPay RSA验签
     */
    public function testJayaPayRSASign()
    {
        echo "\n\n=== JayaPay RSA验签测试 ===\n";
        
        // 从日志中提取的真实数据（线上失败的签名）
        $testData = [
            'name' => 'JayaPay RSA验签（线上失败案例）',
            'params' => [
                'msg' => 'SUCCESS',
                'code' => '00',
                'method' => 'QRIS',
                'orderNum' => 'JP20250803124116275822',
                'platOrderNum' => 'PT1B166BB8E9004009',
                'payFee' => '2250',
                'payMoney' => '50000',
                'phone' => '081234567890',
                'name' => 'User',
                'email' => '<EMAIL>',
                'status' => 'SUCCESS',
                'platSign' => 'ck1ZZJFYa4GpzgNHDfMn8gXeQfPci6Ao7Uu8xBFwjlPmkLZBCOaZ7V/aIz9HSzH8WzrtR/Cm9579trJxvU2Sm5qXPzhTqNVdttFbDywWJq4vw1RWO4czmu+gcGefQ3xezoPOHyWagxg45gui+r/wsENzydj2BShXGNs72maNoD4='
            ]
        ];
        
        echo "\n--- {$testData['name']} ---\n";
        $params = $testData['params'];
        $platSign = $params['platSign'];
        
        echo "接收到的签名: " . substr($platSign, 0, 50) . "...\n";
        
        // 从配置文件获取公钥
        $configFile = 'config/payment_config.php';
        if (file_exists($configFile)) {
            $config = include($configFile);
            
            // 获取平台公钥
            $platformPublicKey = $config['jaya_pay']['platform_public_key'] ?? '';

            // 测试商户公钥
            $testPublicKey = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDIKQvlMBCnfKnt6GtXBl31U8nPnFmo6GMVXwawDuj/pJKHEl0DnslIOWm5FaL9eLF/eB8rUIU5Z7cUKaVU6NEkmXvE9FCyCKEHQMIRsB9GBfrOO06/mKRDxrH9E3SJ7dO7TBdfBZIK5ZFomGcmU+uv2XlDs/g0a9ZnyxtuAdY2bwIDAQAB';

            if ($platformPublicKey) {
                echo "\n=== 测试当前公钥 ===\n";
                echo "当前公钥: " . substr($platformPublicKey, 0, 50) . "...\n";
                echo "\n使用JayaPayService验签:\n";
                echo "平台公钥: " . substr($platformPublicKey, 0, 50) . "...\n";
                
                try {
                    // 详细诊断RSA解密问题
                    echo "\n=== RSA解密诊断 ===\n";

                    // 1. 检查签名Base64解码
                    $encryptedData = base64_decode($platSign);
                    echo "Base64解码后长度: " . strlen($encryptedData) . " bytes\n";

                    // 2. 检查公钥格式
                    if (strpos($platformPublicKey, '-----BEGIN') === false) {
                        $formattedKey = "-----BEGIN PUBLIC KEY-----\n" . chunk_split($platformPublicKey, 64, "\n") . "-----END PUBLIC KEY-----\n";
                    } else {
                        $formattedKey = $platformPublicKey;
                    }

                    $publicKeyResource = openssl_pkey_get_public($formattedKey);
                    if (!$publicKeyResource) {
                        echo "❌ 公钥格式错误: " . openssl_error_string() . "\n";
                    } else {
                        echo "✅ 公钥格式正确\n";

                        // 3. 获取密钥信息
                        $keyDetails = openssl_pkey_get_details($publicKeyResource);
                        $keySize = $keyDetails['bits'];
                        $maxBlock = intval($keySize / 8);
                        echo "密钥长度: {$keySize} bits, 最大块大小: {$maxBlock} bytes\n";
                        echo "加密数据长度: " . strlen($encryptedData) . " bytes\n";

                        // 4. 尝试解密
                        $decrypted = '';
                        $result = openssl_public_decrypt($encryptedData, $decrypted, $publicKeyResource);
                        if (!$result) {
                            echo "❌ RSA解密失败: " . openssl_error_string() . "\n";

                            // 尝试不同的填充方式
                            echo "尝试OAEP填充...\n";
                            $result2 = openssl_public_decrypt($encryptedData, $decrypted, $publicKeyResource, OPENSSL_PKCS1_OAEP_PADDING);
                            if (!$result2) {
                                echo "❌ OAEP填充也失败: " . openssl_error_string() . "\n";
                            } else {
                                echo "✅ OAEP填充成功: " . $decrypted . "\n";
                            }
                        } else {
                            echo "✅ RSA解密成功: " . $decrypted . "\n";
                        }

                        openssl_free_key($publicKeyResource);
                    }

                    // 使用JayaPayService的验签方法
                    echo "\n=== 使用JayaPayService验签（当前公钥） ===\n";
                    $verifyResult = $this->jayaPayService->verifyRSASign($params, $platformPublicKey);
                    echo "验签结果: " . ($verifyResult ? '✓ 成功' : '✗ 失败') . "\n";

                    // 测试商户公钥
                    echo "\n=== 测试商户公钥 ===\n";
                    echo "测试公钥: " . substr($testPublicKey, 0, 50) . "...\n";

                    try {
                        // 详细诊断RSA解密问题（测试公钥）
                        echo "\n=== RSA解密诊断（测试公钥） ===\n";

                        // 检查公钥格式
                        if (strpos($testPublicKey, '-----BEGIN') === false) {
                            $formattedTestKey = "-----BEGIN PUBLIC KEY-----\n" . chunk_split($testPublicKey, 64, "\n") . "-----END PUBLIC KEY-----\n";
                        } else {
                            $formattedTestKey = $testPublicKey;
                        }

                        $testKeyResource = openssl_pkey_get_public($formattedTestKey);
                        if (!$testKeyResource) {
                            echo "❌ 测试公钥格式错误: " . openssl_error_string() . "\n";
                        } else {
                            echo "✅ 测试公钥格式正确\n";

                            // 尝试解密
                            $decrypted = '';
                            $result = openssl_public_decrypt($encryptedData, $decrypted, $testKeyResource);
                            if (!$result) {
                                echo "❌ 测试公钥RSA解密失败: " . openssl_error_string() . "\n";
                            } else {
                                echo "✅ 测试公钥RSA解密成功: " . $decrypted . "\n";
                            }

                            openssl_free_key($testKeyResource);
                        }

                        // 使用测试公钥验签
                        echo "\n=== 使用测试公钥验签 ===\n";
                        $testVerifyResult = $this->jayaPayService->verifyRSASign($params, $testPublicKey);
                        echo "测试公钥验签结果: " . ($testVerifyResult ? '✓ 成功' : '✗ 失败') . "\n";

                    } catch (Exception $e) {
                        echo "测试公钥验签异常: " . $e->getMessage() . "\n";
                    }

                } catch (Exception $e) {
                    echo "验签异常: " . $e->getMessage() . "\n";
                }
            } else {
                echo "配置文件中未找到平台公钥\n";
            }
        } else {
            echo "配置文件不存在: {$configFile}\n";
        }
    }

    /**
     * 测试线上统一回调接口
     */
    public function testOnlineCallback()
    {
        // 使用日志中正式商户的提现回调数据（验签成功的）
        $callbackData = [
            'tradeResult' => '1',
            'merTransferId' => '202508031110458799654977',
            'merNo' => '200999218',
            'tradeNo' => '708578988',
            'transferAmount' => '50000.00',
            'signType' => 'MD5',
            'applyDate' => '2025-08-03 11:11:11',
            'version' => '1.0',
            'respCode' => 'SUCCESS',
            'sign' => '539c3856dda6c71235d841683c83affe'
        ];

        echo "\n--- WatchPay提现回调测试 ---\n";
        echo "回调数据: " . json_encode($callbackData, JSON_UNESCAPED_UNICODE) . "\n";

        // 线上统一回调接口URL
        $callbackUrl = 'https://www.lotteup.com/api/transaction/unifiedWithdrawalCallback';

        echo "\n调用线上接口: {$callbackUrl}\n";

        // 发送POST请求
        $result = $this->sendPostRequest($callbackUrl, $callbackData);

        if ($result !== false) {
            echo "✅ 请求发送成功\n";
            echo "响应内容: " . $result . "\n";
        } else {
            echo "❌ 请求发送失败\n";
        }

        // 测试充值回调（使用正式商户数据）
        echo "\n--- WatchPay充值回调测试 ---\n";

        $rechargeCallbackData = [
            'tradeResult' => '1',
            'oriAmount' => '50000.00',
            'amount' => '50000.00',
            'mchId' => '200999218',  // 正式商户
            'orderNo' => '20000912675836',  // 从日志中的正式商户订单
            'mchOrderNo' => 'GP202508031259478332',
            'signType' => 'MD5',
            'orderDate' => '2025-08-03 12:59:47'
        ];

        // 生成正确的签名
        $rechargeCallbackData['sign'] = $this->watchPayService->generateMD5Sign($rechargeCallbackData, 'YINGPTEMYQ7BAKOSHCAYZESK1WKU8XMK');

        echo "充值回调数据: " . json_encode($rechargeCallbackData, JSON_UNESCAPED_UNICODE) . "\n";

        // 线上统一回调接口URL
        $rechargeCallbackUrl = 'https://www.lotteup.com/api/transaction/unifiedCallback';

        echo "\n调用线上接口: {$rechargeCallbackUrl}\n";

        // 发送POST请求
        $rechargeResult = $this->sendPostRequest($rechargeCallbackUrl, $rechargeCallbackData);

        if ($rechargeResult !== false) {
            echo "✅ 充值回调请求发送成功\n";
            echo "响应内容: " . $rechargeResult . "\n";
        } else {
            echo "❌ 充值回调请求发送失败\n";
        }
    }

    /**
     * 发送POST请求
     */
    private function sendPostRequest($url, $data)
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded',
            'User-Agent: WatchPay-Callback/1.0'
        ]);

        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);

        curl_close($ch);

        if ($error) {
            echo "CURL错误: " . $error . "\n";
            return false;
        }

        echo "HTTP状态码: " . $httpCode . "\n";

        return $result;
    }

    /**
     * 测试JayaPay回调处理
     */
    private function testJayaPayCallback()
    {
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "=== JayaPay回调处理测试 ===\n";
        echo str_repeat("=", 60) . "\n";

        // 测试案例1：真实的成功回调数据
        echo "\n--- 测试案例1：真实成功回调 ---\n";
        $realCallbackData = [
            'msg' => 'SUCCESS',
            'code' => '00',
            'method' => 'QRIS',
            'orderNum' => 'JP20250803124116275822',
            'platOrderNum' => 'PT1B166BB8E9004009',
            'payFee' => '2250',
            'payMoney' => '50000',
            'phone' => '081234567890',
            'name' => 'User',
            'email' => '<EMAIL>',
            'status' => 'SUCCESS',
            'platSign' => 'ck1ZZJFYa4GpzgNHDfMn8gXeQfPci6Ao7Uu8xBFwjlPmkLZBCOaZ7V/aIz9HSzH8WzrtR/Cm9579trJxvU2Sm5qXPzhTqNVdttFbDywWJq4vw1RWO4czmu+gcGefQ3xezoPOHyWagxg45gui+r/wsENzydj2BShXGNs72maNoD4='
        ];

        $this->testSingleJayaPayCallback($realCallbackData, "真实订单回调");

        // 测试案例2：另一个真实回调数据
        echo "\n--- 测试案例2：另一个真实回调 ---\n";
        $anotherRealData = [
            'msg' => 'SUCCESS',
            'code' => '00',
            'method' => 'QRIS',
            'orderNum' => 'JP20250803130647189534',
            'platOrderNum' => 'PT1B167190BE403009',
            'payFee' => '2250',
            'payMoney' => '50000',
            'phone' => '081234567890',
            'name' => 'User',
            'email' => '<EMAIL>',
            'status' => 'SUCCESS',
            'platSign' => 'cLnlsaIIsQl7YaSPTQxY+9bUuVCYDXqUcl9ojmk5Loh+IzT+72dU4rjif+qRxo2HEmZ3Qtk7Qb5huqjBf91ZjmUxR3OCm32icEdh5LPW6mSjq3KkrYalWid/DnddVh8o64jL1smybWGusxM2k4so+amjoc/J43OGDZQ1FjNGMh4='
        ];

        $this->testSingleJayaPayCallback($anotherRealData, "另一个真实订单回调");

        // 测试案例3：生成的测试数据
        echo "\n--- 测试案例3：生成的测试数据 ---\n";
        $testData = [
            'msg' => 'SUCCESS',
            'code' => '00',
            'method' => 'QRIS',
            'orderNum' => 'JP' . date('YmdHis') . rand(1000, 9999),
            'platOrderNum' => 'PT1B' . strtoupper(substr(md5(time()), 0, 12)),
            'payFee' => '2250',
            'payMoney' => '50000',
            'phone' => '081234567890',
            'name' => 'TestUser',
            'email' => '<EMAIL>',
            'status' => 'SUCCESS'
        ];

        // 生成签名
        $testSign = $this->generateJayaPayTestSign($testData);
        if ($testSign) {
            $testData['platSign'] = $testSign;
            $this->testSingleJayaPayCallback($testData, "生成的测试数据回调");
        }
    }

    /**
     * 测试单个JayaPay回调
     */
    private function testSingleJayaPayCallback($callbackData, $testName)
    {
        echo "测试名称: {$testName}\n";
        echo "订单号: {$callbackData['orderNum']}\n";
        echo "平台订单号: {$callbackData['platOrderNum']}\n";
        echo "金额: {$callbackData['payMoney']}\n";

        // 1. 验证签名
        echo "\n1. 签名验证:\n";
        $config = include 'config/payment_config.php';
        $publicKey = $config['jaya_pay']['platform_public_key'] ?? '';

        if (empty($publicKey)) {
            echo "❌ 公钥未配置\n";
            return;
        }

        $jayaPayService = new \app\common\service\JayaPayService();
        $signValid = $jayaPayService->verifyRSASign($callbackData, $publicKey);
        echo "签名验证结果: " . ($signValid ? "✅ 通过" : "❌ 失败") . "\n";

        // 2. 测试回调处理
        echo "\n2. 回调处理测试:\n";
        $callbackResult = $jayaPayService->handleRechargeCallback($callbackData);
        echo "回调处理结果: " . ($callbackResult ? "✅ 成功" : "❌ 失败") . "\n";

        // 3. 发送到线上接口
        echo "\n3. 线上接口测试:\n";
        $response = $this->sendPostRequest('https://www.lotteup.com/api/transaction/unifiedCallback', $callbackData);
        if ($response !== false) {
            echo "线上接口响应: " . $response . "\n";
            echo "接口调用结果: " . ($response === 'success' ? "✅ 成功" : "❌ 失败") . "\n";
        } else {
            echo "❌ 接口调用失败\n";
        }

        echo str_repeat("-", 50) . "\n";
    }

    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        echo "开始验签测试...\n";
        echo "使用TransactionService和JayaPayService的验签方法\n";
        echo str_repeat("=", 60) . "\n";
        
        $this->testWatchPayMD5Sign();
        $this->testJayaPayRSASign();

        echo "\n" . str_repeat("=", 60) . "\n";
        echo "验签测试完成\n";

        // JayaPay回调处理测试
        $this->testJayaPayCallback();

        // 调用线上统一回调接口测试
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "=== 调用线上统一回调接口测试 ===\n";
        echo str_repeat("=", 60) . "\n";

        $this->testOnlineCallback();
    }
}

// 运行测试
try {
    $test = new SignVerificationTest();
    $test->runAllTests();
} catch (Exception $e) {
    echo "测试异常: " . $e->getMessage() . "\n";
}
