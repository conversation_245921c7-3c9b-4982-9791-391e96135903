<?php /*a:1:{s:59:"/var/www/html/application/manage/view/bank/receivables.html";i:**********;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>收款账户</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
    <div style="padding: 20px; background-color: #F2F2F2;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <table class="layui-hide" id="receivables" lay-filter="receivables"></table>
                </div>
            </div>
        </div>
    </div>
    <!-- 头部左侧工具栏 -->
    <script type="text/html" id="toolbarDemo">
        <div class="layui-btn-container layui-btn-group">
            <button type="button" class="layui-btn layui-btn-sm" lay-event="receivablesQrcodeAdd">添加收款二维码</button>
            <button type="button" class="layui-btn layui-btn-normal layui-btn-sm" lay-event="receivablesAdd">添加收款账户</button>
        </div>
    </script>
    <!-- 表单元素 -->
    <script type="text/html" id="state">
        <input type="checkbox" name="state" value="{{d.id}}" lay-skin="switch" lay-text="开|关" lay-filter="receivablesState" {{ d.state == 1 ? 'checked' : '' }}>
    </script>
    <script type="text/html" id="openLevel">
        <!-- <div class="layui-btn-group"> -->
            <input type="checkbox" name="open-level[0]" value="0" title="VIP0" data-openlevelid="{{d.id}}" lay-filter="open-level" {{ d.openLevel0 == 1 ? 'checked' : '' }}>
            <input type="checkbox" name="open-level[1]" value="1" title="VIP1" data-openlevelid="{{d.id}}" lay-filter="open-level" {{ d.openLevel1 == 1 ? 'checked' : '' }}>
            <input type="checkbox" name="open-level[2]" value="2" title="VIP2" data-openlevelid="{{d.id}}" lay-filter="open-level" {{ d.openLevel2 == 1 ? 'checked' : '' }}>
            <input type="checkbox" name="open-level[3]" value="3" title="VIP3" data-openlevelid="{{d.id}}" lay-filter="open-level" {{ d.openLevel3 == 1 ? 'checked' : '' }}>
            <input type="checkbox" name="open-level[4]" value="4" title="VIP4" data-openlevelid="{{d.id}}" lay-filter="open-level" {{ d.openLevel4 == 1 ? 'checked' : '' }}>
            <input type="checkbox" name="open-level[5]" value="5" title="VIP5" data-openlevelid="{{d.id}}" lay-filter="open-level" {{ d.openLevel5 == 1 ? 'checked' : '' }}>
            <input type="checkbox" name="open-level[6]" value="6" title="VIP6" data-openlevelid="{{d.id}}" lay-filter="open-level" {{ d.openLevel6 == 1 ? 'checked' : '' }}>
        <!-- </div> -->
    </script>
    <script type="text/html" id="action">
        <div class="layui-btn-group">
            {{# if (d.qrcode) { }}
            <button type="button" class="layui-btn layui-btn-xs" lay-event="receivablesQrcodeEdit">
                <i class="layui-icon">&#xe642;</i>
            </button>
            {{# } else { }}
            <button type="button" class="layui-btn layui-btn-xs" lay-event="receivablesEdit">
                <i class="layui-icon">&#xe642;</i>
            </button>
            {{# } }}
            <button type="button" class="layui-btn layui-btn-danger layui-btn-xs" lay-event="receivablesDelete">
                <i class="layui-icon">&#xe640;</i>
            </button>
        </div>
    </script>

<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script src="/resource/js/manage/bank.js"></script>
<script>
    layui.use(['table'], function(){
        var $  = layui.$
        ,table = layui.table;

        //方法级渲染
        table.render({
            elem: '#receivables'
            ,title: '收款账户'
            ,url: '/manage/bank/receivables'
            ,method: 'post'
            ,cols: [[
                {checkbox: true, fixed: true}
                ,{field: 'name', title: '收款人', sort: true, fixed: 'left'}
                ,{field: 'account', title: '收款账号', sort: true, fixed: 'left',width:400}
                ,{field: 'bank_name', title: '开户行', sort: true}
                ,{field: 'rname', title: '对应渠道', sort: true}
                ,{field: 'state', title: '当前状态', sort: true, templet: '#state', unresize: true}
                ,{title: '操作', width: '20%', toolbar: '#action'}
            ]]
            ,cellMinWidth: 100
            ,toolbar: '#toolbarDemo'
            ,defaultToolbar: ['filter', 'print', 'exports']
            ,totalRow: true
            ,page: {
                layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip']
            }
            ,skin: 'row' //行边框风格
            ,even: true //开启隔行背景
        });

        //监听排序事件
        table.on('sort(receivables)', function(obj){ //注：sort 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
            //尽管我们的 table 自带排序功能，但并没有请求服务端。
            //有些时候，你可能需要根据当前排序的字段，重新向服务端发送请求，从而实现服务端排序，如：
            table.reload('receivables', {
                initSort: obj //记录初始排序，如果不设的话，将无法标记表头的排序状态。
                ,where: { //请求参数（注意：这里面的参数可任意定义，并非下面固定的格式）
                    sortField: obj.field //排序字段
                    ,sortType: obj.type //排序方式
                }
            });
        });

        active = {
            search: function(){
                //执行重载
                table.reload('receivables', {
                    page: {
                        curr: 1 //重新从第 1 页开始
                    }
                    ,where: {
                        username: $("input[name='username']").val()
                        ,state: $("select[name='state'] option:selected").val()
                        ,datetime_range: $("input[name='datetime_range']").val()

                    }
                }, 'data');
            }
        };

        $('.search .layui-btn').on('click', function(){
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });
    });
</script>
</body>
</html>
