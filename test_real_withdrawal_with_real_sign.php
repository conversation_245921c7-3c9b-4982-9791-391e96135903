<?php
/**
 * 测试真实的JayaPay提现回调数据（使用真实签名）
 */

echo "=== 测试真实JayaPay提现回调数据（真实签名） ===\n\n";

// 从日志中提取的真实JayaPay提现回调数据（使用真实签名）
$realWithdrawalCallbackData = [
    "bankCode" => "014",
    "fee" => "7000",
    "description" => "代付下单",
    "orderNum" => "202508031506301400899836",
    "feeType" => "1",
    "number" => "*************",
    "platOrderNum" => "YBL1951902536864641079",
    "money" => "50000",
    "statusMsg" => "FAILED",
    "name" => "aa",
    "platSign" => "JI7wpyqiMryARy6GYHungd9XsMqg4GVvXSP7gG2aCxG6L5MK/fBlmm5DPaSkamFRqEBYDIWfkf+1rpKUwuCxyA2w6tg8KLHmjbIgz28c9u6G4eIx3cY3aJ3D0M2p4xxIKSz9mnycKKLO/kd+/fJycZdhAaxL4vRmEleXn1ApTH8=",
    "status" => "4"
];

echo "真实提现回调数据（真实签名）:\n";
echo "订单号: {$realWithdrawalCallbackData['orderNum']}\n";
echo "平台订单号: {$realWithdrawalCallbackData['platOrderNum']}\n";
echo "金额: {$realWithdrawalCallbackData['money']} IDR\n";
echo "手续费: {$realWithdrawalCallbackData['fee']} IDR\n";
echo "状态: {$realWithdrawalCallbackData['statusMsg']}\n";
echo "银行代码: {$realWithdrawalCallbackData['bankCode']}\n";
echo "账户: {$realWithdrawalCallbackData['number']}\n";
echo "用户: {$realWithdrawalCallbackData['name']}\n";
echo "签名: " . substr($realWithdrawalCallbackData['platSign'], 0, 50) . "...\n\n";

// 测试本地环境
echo "=== 本地环境测试 ===\n";
$url = 'http://dianzhan_nginx/api/transaction/unifiedWithdrawalCallback';
echo "回调地址: {$url}\n";

$postData = http_build_query($realWithdrawalCallbackData);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/x-www-form-urlencoded',
    'User-Agent: JayaPay-Callback/1.0'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
$info = curl_getinfo($ch);
curl_close($ch);

echo "HTTP状态码: {$httpCode}\n";
echo "响应时间: " . round($info['total_time'], 2) . "秒\n";

if ($error) {
    echo "❌ CURL错误: {$error}\n";
} else {
    echo "响应内容: {$response}\n";
    
    if ($httpCode === 200) {
        if ($response === 'SUCCESS') {
            echo "✅ 提现回调处理成功！\n";
            echo "✅ 签名验证通过！\n";
            echo "✅ 修复在本地环境正常工作！\n";
        } elseif ($response === 'fail') {
            echo "⚠️ 回调返回fail\n";
            echo "可能原因：\n";
            echo "1. 签名验证失败（需要进一步修复）\n";
            echo "2. 订单状态问题\n";
            echo "3. 订单不存在\n";
        } else {
            echo "⚠️ 回调接收成功，处理结果: {$response}\n";
        }
    } else {
        echo "❌ 回调请求失败，HTTP状态码: {$httpCode}\n";
    }
}

echo "\n=== 测试完成 ===\n";
echo "\n说明：\n";
echo "- 使用了从线上日志中提取的真实JayaPay提现回调数据\n";
echo "- 包含真实的platSign签名\n";
echo "- 测试了提现回调的签名验证修复效果\n";
echo "- 如果仍然返回fail，说明提现回调需要进一步修复\n";
